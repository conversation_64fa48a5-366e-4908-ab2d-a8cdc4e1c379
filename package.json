{"name": "a1d-agent", "version": "1.0.0", "description": "", "main": "src/mastra/index.ts", "scripts": {"preinstall": "lefthook install", "dev": "run-p dev:*", "dev:convex": "dotenvx run -- convex dev", "dev:mastra": "dotenvx run -- mastra dev", "build": "mastra build", "start": "dotenvx run  -- <PERSON><PERSON> start", "env:check": "dotenvx run -- bun scripts/env-check.ts", "env:example": "dotenvx ext genexample", "convex:typegen": "convex-helpers ts-api-spec --output-file src/convex/api.public.ts", "format": "prettier --write --ignore-path .gitignore --ignore-path .prettierignore src/", "typecheck": "tsc --noEmit", "x": "dotenvx run -- bun", "test": "dotenvx run -- vitest run"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/openai": "^1.3.23", "@ai-sdk/provider": "^1.1.3", "@ai-sdk/xai": "^1.2.17", "@aws-sdk/client-s3": "^3.844.0", "@browserbasehq/stagehand": "^2.4.1", "@convex-dev/auth": "^0.0.87", "@fal-ai/client": "^1.6.0", "@google/genai": "^1.9.0", "@mastra/client-js": "^0.10.14", "@mastra/core": "^0.10.15", "@mastra/loggers": "^0.10.3", "@mastra/mcp": "^0.10.6", "@mastra/memory": "^0.11.3", "@mastra/pg": "^0.12.3", "ai": "^4.3.19", "arktype": "^2.1.20", "confbox": "^0.2.2", "convex": "^1.25.2", "convex-helpers": "^0.1.99", "drizzle-orm": "^0.44.3", "fetch-event-stream": "^0.1.5", "hono": "^4.8.4", "lodash-es": "^4.17.21", "openai": "^5.8.3", "postgres": "^3.4.7", "rxjs": "^7.8.2", "sharp": "^0.34.3", "ulid": "^3.0.1", "unctx": "^2.4.1", "zod": "^3.25.67"}, "devDependencies": {"@dotenvx/dotenvx": "^1.48.0", "@ianvs/prettier-plugin-sort-imports": "^4.5.1", "@pulumi/azure-native": "^3.5.1", "@pulumi/pulumi": "^3.184.0", "@smithy/types": "^4.3.1", "@types/bun": "^1.2.18", "@types/lodash-es": "^4.17.12", "@types/node": "^24.0.15", "drizzle-kit": "^0.31.4", "lefthook": "^1.12.2", "mastra": "^0.10.13", "npm-run-all": "^4.1.5", "prettier": "^3.6.2", "type-fest": "^4.41.0", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}