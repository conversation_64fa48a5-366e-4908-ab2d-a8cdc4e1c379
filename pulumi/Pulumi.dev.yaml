config:
  a1d-agent:infraStackRef: "ethan-huo/a1d-pulumi-azure/azure-dev"
  a1d-agent:appName: "a1d-agent-dev"
  a1d-agent:containerImage: "a1dazureacr.azurecr.io/a1d-agent:latest"
  a1d-agent:environment: "development"
  # Resource configuration for LLM chat app
  a1d-agent:cpu: "0.5"        # 0.5 CPU cores - sufficient for chat app
  a1d-agent:memory: "1"       # 1GB memory - sufficient for Node.js chat app
  a1d-agent:port: "4111"      # Match application port
  # Scaling configuration - minimal for chat app
  a1d-agent:minReplicas: "1"  # Keep 1 instance running
  a1d-agent:maxReplicas: "2"  # Max 2 instances (for zero-downtime deployment)
  a1d-agent:scaleToZero: "false"  # Always keep running
