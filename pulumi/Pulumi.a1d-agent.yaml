# Pulumi configuration for A1D Agent deployment
# This file contains the configuration for deploying the A1D Agent AI service

config:
  # Infrastructure stack reference
  infraStackRef: "ethan-huo-org/a1d-infra/dev"

  # Application configuration
  appName: "a1d-agent"
  containerImage: "a1dazureacr.azurecr.io/a1d-agent:latest"
  environment: "production"

  # Resource allocation for LLM chat app
  cpu: 0.5        # 0.5 CPU cores - sufficient for chat app (mainly API forwarding)
  memory: 1       # 1GB memory - sufficient for Node.js chat app
  port: 4111      # Match application's exposed port

  # Scaling configuration - Minimal scaling for chat app
  minReplicas: 1  # Keep 1 instance running for availability
  maxReplicas: 2  # Max 2 instances (mainly for zero-downtime deployment)
  scaleToZero: false  # Disable scale-to-zero for always-on service

  # Domain configuration
  usesEnvironmentLevelDomains: true

  # Secrets (set these using: pulumi config set --secret <key> <value>)
  # postgresUrl: "postgresql://..."  # Set via: pulumi config set --secret postgresUrl "postgresql://..."
  # convexUrl: "https://..."         # Set via: pulumi config set --secret convexUrl "https://..."

  # Additional AI service specific configurations
  # Add more secrets as needed for your integrations:
  # minimaxApiKey: "..."
  # anthropicApiKey: "..."
  # openaiApiKey: "..."
  # etc.
